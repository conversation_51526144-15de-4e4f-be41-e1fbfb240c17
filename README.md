# ISO 27001 Compliance Management System

This repository serves as the central hub for managing our organization's Information Security Management System (ISMS) compliance, primarily structured around ISO 27001:2022, with integrated mappings to NIS2 Directive, NIST Cybersecurity Framework 2.0, and GDPR requirements.

## Repository Structure

### Core ISO 27001 Structure
- **`04-Context-of-Organization/`** - Organizational context, interested parties, and ISMS scope
- **`05-Leadership/`** - Leadership commitment, policy, and organizational roles
- **`06-Planning/`** - Risk management, objectives, and planning for changes
- **`07-Support/`** - Resources, competence, awareness, communication, and documented information
- **`08-Operation/`** - Operational planning, risk assessment, and treatment
- **`09-Performance-Evaluation/`** - Monitoring, internal audit, and management review
- **`10-Improvement/`** - Nonconformity, corrective action, and continual improvement

### Annex A Controls
- **`Annex-A-Controls/`** - Detailed implementation of all ISO 27001 Annex A security controls
  - Organized by control domains (A.5 through A.8)
  - Individual markdown files for each control

### Supporting Frameworks
- **`_Crosswalks/`** - Framework mapping documents
- **`GDPR/`** - GDPR-specific documentation and procedures
- **`NIS2-Specific/`** - NIS2 Directive specific requirements
- **`Templates/`** - Document templates for policies, procedures, and assessments
- **`Evidence/`** - Links and references to compliance evidence

## Key Documents
- **Statement of Applicability (SoA)** - `Statement_of_Applicability_SoA.md`
- **Risk Register** - `Risk_Register.md`
- **Main Information Security Policy** - `Information_Security_Policy_Main.md`

## Workflow and Contribution Guidelines

### Branch Naming Conventions
- `feat/update-policy-A.X.Y.Z` - For policy updates
- `docs/clarify-clause-X.Y` - For documentation clarifications
- `audit/finding-YYYY-MM-DD` - For audit findings and remediation
- `review/management-YYYY-QX` - For management review updates

### Pull Request Process
1. Create a feature branch from `main`
2. Make your changes with clear, descriptive commit messages
3. Submit a PR with:
   - Clear description of changes
   - Reference to related issues
   - Impact assessment on compliance posture
4. Require at least one review from designated approvers
5. Merge only after approval and CI checks

### Issue Management
Use GitHub Issues to track:
- **Tasks**: Implementation of specific controls or documents
- **Gaps**: Identified compliance gaps requiring attention
- **Non-conformities**: Audit findings and corrective actions
- **Improvements**: Continual improvement initiatives

#### Issue Labels
- `iso27001`, `gdpr`, `nis2`, `nist-csf` - Framework tags
- `policy-draft`, `procedure-update` - Document type tags
- `evidence-needed`, `audit-finding` - Status tags
- `high-priority`, `medium-priority`, `low-priority` - Priority tags

### Review and Approval Process
- **Information Security Manager**: Primary reviewer for all security-related changes
- **Data Protection Officer**: Required reviewer for GDPR-related changes
- **Legal/Compliance Team**: Required reviewer for regulatory interpretation
- **Management**: Required approval for policy-level changes

## Framework Integration

This repository implements a multi-framework approach:

1. **Primary Structure**: ISO 27001:2022 serves as the foundational framework
2. **Mapped Frameworks**: 
   - NIS2 Directive requirements mapped to ISO controls
   - NIST CSF 2.0 functions and categories cross-referenced
   - GDPR articles integrated with relevant security controls

### Metadata Standards
Each control document includes metadata headers:
```markdown
---
ISO-27001-Control: A.X.Y.Z
NIST-CSF-2.0-Mappings: [ID.GV-01, PR.PS-01]
GDPR-Article-Mappings: [Art. 32.1a, Art. 5.1f]
NIS2-Article-Mappings: [Art. 21.2a]
Last-Updated: YYYY-MM-DD
Review-Date: YYYY-MM-DD
Owner: [Role/Department]
---
```

## Getting Started

1. **New Team Members**: Review this README and the templates in `Templates/`
2. **Document Updates**: Use the appropriate template and follow the PR process
3. **Evidence Collection**: Link evidence in the `Evidence/` folder structure
4. **Regular Reviews**: Participate in scheduled management reviews and internal audits

## Compliance Status Dashboard

Track our compliance posture through:
- GitHub Issues (open/closed ratio by framework)
- PR activity (documentation currency)
- Evidence collection status
- Internal audit findings and remediation

## Contact and Support

- **Information Security Manager**: [Contact Information]
- **Data Protection Officer**: [Contact Information]
- **Compliance Team**: [Contact Information]

---

*This repository is confidential and proprietary. Access is restricted to authorized personnel only.*
# compliance
