# 6.1.2 Information Security Risk Assessment Methodology

---
ISO-27001-Requirement: 6.1.2
NIST-CSF-2.0-Mappings: [ID.RA-01, ID.RA-02, ID.RA-03, ID.RA-04, ID.RA-05]
GDPR-Article-Mappings: [Art. 25, Art. 32, Art. 35]
NIS2-Article-Mappings: [Art. 21.1]
Last-Updated: [YYYY-MM-DD]
Review-Date: [YYYY-MM-DD]
Version: 1.0
Owner: [Information Security Manager]
---

## Purpose
This document defines the methodology for conducting information security risk assessments within the organization's Information Security Management System (ISMS). It establishes a systematic approach to identify, analyze, and evaluate information security risks.

## Scope
This methodology applies to all information assets, systems, processes, and activities within the scope of the ISMS as defined in [4.3 Determining the Scope of the ISMS](../04-Context-of-Organization/4.3_Scope_of_ISMS.md).

## Risk Assessment Framework

### Risk Assessment Principles
**Systematic Approach**
- Structured and repeatable methodology
- Consistent application across the organization
- Integration with business processes
- Alignment with organizational risk appetite

**Risk-Based Decision Making**
- Informed decision-making based on risk analysis
- Proportionate response to identified risks
- Cost-effective risk treatment options
- Continuous monitoring and review

**Stakeholder Involvement**
- Engagement of relevant stakeholders
- Business context and expertise integration
- Clear communication of risks and treatments
- Shared understanding of risk management

### Risk Assessment Methodology

#### Step 1: Asset Identification and Valuation
**Asset Inventory**
- Comprehensive identification of information assets
- Asset categorization and classification
- Asset ownership and custodian assignment
- Asset dependency and relationship mapping

**Asset Valuation**
- Business value assessment
- Replacement cost evaluation
- Revenue impact analysis
- Regulatory and compliance value
- Reputation and brand impact

**Valuation Criteria**
- **High Value (3)**: Critical to business operations, high financial impact
- **Medium Value (2)**: Important to operations, moderate financial impact
- **Low Value (1)**: Standard operations, minimal financial impact

#### Step 2: Threat Identification
**Threat Categories**
- **Natural Threats**: Natural disasters, environmental hazards
- **Human Threats**: Malicious insiders, external attackers, human error
- **Technical Threats**: System failures, software vulnerabilities, hardware malfunctions
- **Environmental Threats**: Power failures, infrastructure disruptions

**Threat Sources**
- **Internal Sources**: Employees, contractors, business partners
- **External Sources**: Cybercriminals, competitors, nation-states, hacktivists
- **Environmental Sources**: Natural disasters, infrastructure failures

**Threat Intelligence**
- Industry threat reports and intelligence
- Government and law enforcement advisories
- Vendor security bulletins and alerts
- Internal incident and event analysis
- Peer organization information sharing

#### Step 3: Vulnerability Assessment
**Vulnerability Categories**
- **Technical Vulnerabilities**: Software flaws, configuration weaknesses, design defects
- **Physical Vulnerabilities**: Facility security gaps, equipment vulnerabilities
- **Administrative Vulnerabilities**: Policy gaps, procedure weaknesses, training deficiencies
- **Operational Vulnerabilities**: Process weaknesses, human factors, organizational issues

**Vulnerability Identification Methods**
- Automated vulnerability scanning
- Manual security assessments
- Penetration testing and ethical hacking
- Code review and security testing
- Physical security assessments
- Process and procedure reviews

**Vulnerability Severity Rating**
- **High (3)**: Easily exploitable, significant impact potential
- **Medium (2)**: Moderately exploitable, moderate impact potential
- **Low (1)**: Difficult to exploit, limited impact potential

#### Step 4: Risk Analysis
**Risk Calculation**
Risk = Asset Value × Threat Likelihood × Vulnerability Severity

**Likelihood Assessment**
- **High (3)**: Very likely to occur within 1 year (>50% probability)
- **Medium (2)**: Moderately likely to occur within 1-3 years (10-50% probability)
- **Low (1)**: Unlikely to occur within 3 years (<10% probability)

**Impact Assessment**
- **Confidentiality Impact**: Unauthorized disclosure consequences
- **Integrity Impact**: Unauthorized modification consequences
- **Availability Impact**: Service disruption consequences

**Impact Levels**
- **High (3)**: Severe impact on business operations, reputation, or compliance
- **Medium (2)**: Moderate impact with manageable consequences
- **Low (1)**: Minor impact with minimal consequences

#### Step 5: Risk Evaluation
**Risk Matrix**
| Likelihood | Low Impact (1) | Medium Impact (2) | High Impact (3) |
|------------|----------------|-------------------|-----------------|
| High (3)   | Medium (3)     | High (6)          | Critical (9)    |
| Medium (2) | Low (2)        | Medium (4)        | High (6)        |
| Low (1)    | Low (1)        | Low (2)           | Medium (3)      |

**Risk Levels**
- **Critical (7-9)**: Immediate action required, senior management attention
- **High (5-6)**: Priority action required, management attention
- **Medium (3-4)**: Planned action required, regular monitoring
- **Low (1-2)**: Acceptable risk, periodic review

**Risk Acceptance Criteria**
- **Critical and High Risks**: Require immediate risk treatment
- **Medium Risks**: Require planned risk treatment within defined timeframes
- **Low Risks**: May be accepted with appropriate monitoring

### Risk Assessment Process

#### Risk Assessment Planning
**Assessment Scope**
- Define assessment boundaries and objectives
- Identify assets and systems to be assessed
- Determine assessment methodology and approach
- Allocate resources and establish timeline

**Assessment Team**
- Risk assessment coordinator
- Asset owners and subject matter experts
- Technical specialists and security professionals
- Business representatives and stakeholders

#### Risk Assessment Execution
**Information Gathering**
- Asset inventory and valuation
- Threat intelligence and analysis
- Vulnerability assessment and testing
- Control effectiveness evaluation
- Stakeholder interviews and workshops

**Risk Analysis and Documentation**
- Risk scenario development and analysis
- Risk calculation and evaluation
- Risk register creation and maintenance
- Risk treatment recommendations
- Stakeholder communication and validation

#### Risk Assessment Review and Approval
**Quality Assurance**
- Risk assessment methodology compliance
- Risk analysis accuracy and completeness
- Risk evaluation consistency and objectivity
- Documentation quality and clarity

**Approval Process**
- Risk assessment team review and validation
- Asset owner review and acceptance
- Information Security Manager approval
- Senior management review and endorsement

### Risk Assessment Documentation

#### Risk Register
**Risk Identification**
- Unique risk identifier
- Risk description and scenario
- Asset(s) affected
- Threat source and vulnerability
- Risk owner and stakeholder

**Risk Analysis**
- Asset value rating
- Threat likelihood rating
- Vulnerability severity rating
- Impact assessment (CIA)
- Risk level calculation

**Risk Treatment**
- Current control effectiveness
- Residual risk assessment
- Risk treatment options
- Treatment recommendations
- Implementation timeline

#### Risk Assessment Report
**Executive Summary**
- Key findings and recommendations
- Risk landscape overview
- Critical and high-risk summary
- Resource and investment requirements

**Detailed Analysis**
- Methodology and approach
- Asset inventory and valuation
- Threat and vulnerability analysis
- Risk evaluation and prioritization
- Treatment recommendations

### Risk Assessment Frequency and Triggers

#### Regular Assessments
- **Annual Comprehensive Assessment**: Complete risk assessment of all assets
- **Quarterly Updates**: Review and update of high and critical risks
- **Monthly Monitoring**: Ongoing risk monitoring and threat intelligence

#### Event-Triggered Assessments
- Significant organizational changes
- New system or technology implementations
- Major security incidents or breaches
- Regulatory or compliance changes
- Threat landscape changes

### Risk Assessment Tools and Techniques

#### Quantitative Methods
- **Asset Valuation Models**: Financial and business impact models
- **Probability Analysis**: Statistical and historical data analysis
- **Monte Carlo Simulation**: Risk modeling and scenario analysis
- **Cost-Benefit Analysis**: Treatment option evaluation

#### Qualitative Methods
- **Expert Judgment**: Subject matter expert assessment
- **Scenario Analysis**: Risk scenario development and evaluation
- **Delphi Technique**: Consensus-based risk assessment
- **Brainstorming**: Collaborative risk identification

#### Automated Tools
- **Vulnerability Scanners**: Automated vulnerability identification
- **Risk Assessment Software**: Risk calculation and management tools
- **Threat Intelligence Platforms**: Automated threat analysis
- **Asset Discovery Tools**: Automated asset identification and inventory

## Related Documents
- [4.1 Understanding the Organization and Its Context](../04-Context-of-Organization/4.1_Organizational_Context.md)
- [6.1.3 Information Security Risk Treatment](./6.1.3_Risk_Treatment_Plan.md)
- [Risk Register](../Risk_Register.md)
- [Statement of Applicability](../Statement_of_Applicability_SoA.md)

## Review and Maintenance
This methodology shall be reviewed annually and updated as needed to reflect:
- Changes in organizational context and business environment
- Evolution of threat landscape and attack techniques
- Regulatory and compliance requirement changes
- Lessons learned from risk assessments and incidents
- Stakeholder feedback and improvement opportunities

---
*Document Classification: Internal Use Only*
*Next Review Date: [YYYY-MM-DD]*
