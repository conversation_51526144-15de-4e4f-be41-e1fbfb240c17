# NIST Cybersecurity Framework 2.0 to ISO 27001 Mapping

---
Document-Type: Framework Mapping
Primary-Framework: ISO 27001:2022
Secondary-Framework: NIST CSF 2.0
Last-Updated: [YYYY-MM-DD]
Review-Date: [YYYY-MM-DD]
Version: 1.0
Owner: [Information Security Manager]
---

## Purpose
This document provides a comprehensive mapping between NIST Cybersecurity Framework 2.0 Functions, Categories, and Subcategories to ISO 27001:2022 controls and clauses. This mapping helps ensure our ISO 27001 implementation addresses NIST CSF requirements and facilitates dual-framework compliance.

## Mapping Methodology
- **Direct Mapping**: ISO control directly addresses NIST subcategory
- **Partial Mapping**: ISO control partially addresses NIST subcategory
- **Gap**: NIST subcategory not fully addressed by current ISO controls
- **Multiple Controls**: NIST subcategory addressed by multiple ISO controls

## GOVERN (GV) Function

### GV.OC: Organizational Context
| NIST CSF 2.0 Subcategory | Description | ISO 27001 Mapping | Mapping Type | Notes |
|---------------------------|-------------|-------------------|--------------|-------|
| GV.OC-01 | The organizational mission is understood and informs cybersecurity risk management | 4.1, 4.3 | Direct | Organizational context and ISMS scope |
| GV.OC-02 | Internal and external stakeholders are understood, and their needs and expectations regarding cybersecurity risk management are understood | 4.2 | Direct | Understanding interested parties |
| GV.OC-03 | Legal, regulatory, and contractual requirements regarding cybersecurity are understood and managed | A.18.1.1, A.18.1.2 | Direct | Compliance requirements identification |
| GV.OC-04 | Critical objectives, capabilities, and services that stakeholders depend on or expect from the organization are understood and communicated | 4.1, 4.2, 4.3 | Direct | Context and stakeholder analysis |
| GV.OC-05 | Outcomes, capabilities, and services that the organization depends on are understood and communicated | 4.1, A.15.1.1 | Partial | Supplier relationship management |

### GV.RM: Risk Management Strategy
| NIST CSF 2.0 Subcategory | Description | ISO 27001 Mapping | Mapping Type | Notes |
|---------------------------|-------------|-------------------|--------------|-------|
| GV.RM-01 | Risk management objectives are established and agreed to by organizational stakeholders | 6.1.1, 6.2 | Direct | Risk management and objectives |
| GV.RM-02 | Risk appetite and risk tolerance are established, communicated, and maintained | 6.1.1 | Partial | Risk criteria definition |
| GV.RM-03 | Cybersecurity risk management activities and outcomes are included in enterprise risk reporting | 9.3 | Partial | Management review reporting |
| GV.RM-04 | Strategic direction that describes appropriate risk response is established and communicated | 6.1.3 | Direct | Risk treatment planning |
| GV.RM-05 | Lines of communication across the organization are established for cybersecurity risks, including escalation pathways for cybersecurity incidents | A.16.1.2, A.6.1.1 | Direct | Incident reporting and communication |
| GV.RM-06 | A standardized method for calculating, documenting, categorizing, and prioritizing cybersecurity risks is established and communicated | 6.1.2 | Direct | Risk assessment methodology |
| GV.RM-07 | Strategic opportunities (i.e., positive risks) are characterized and are included in organizational cybersecurity risk discussions | 6.1.1 | Partial | Risk identification includes opportunities |

### GV.SC: Supply Chain Risk Management
| NIST CSF 2.0 Subcategory | Description | ISO 27001 Mapping | Mapping Type | Notes |
|---------------------------|-------------|-------------------|--------------|-------|
| GV.SC-01 | A cybersecurity supply chain risk management program, strategy, objectives, policies, and processes are established and agreed to by organizational stakeholders | A.15.1.1, A.15.1.2 | Direct | Supplier relationship security |
| GV.SC-02 | Cybersecurity roles, responsibilities, and authorities for supply chain risk management are established, communicated, and coordinated internally and externally | A.15.1.1 | Partial | Supplier security responsibilities |
| GV.SC-03 | Cybersecurity supply chain risk management is integrated into cybersecurity and enterprise risk management, risk assessment, and improvement processes | 6.1.2, A.15.1.3 | Partial | Supplier risk assessment |
| GV.SC-04 | Suppliers are known and prioritized by criticality | A.15.1.1 | Partial | Supplier inventory and classification |
| GV.SC-05 | Requirements to address cybersecurity risks in supply chains are established, prioritized, and integrated into contracts and other types of agreements with suppliers and other relevant third parties | A.15.1.2, A.13.2.1 | Direct | Supplier agreements and contracts |
| GV.SC-06 | Planning and due diligence are performed to reduce risks before entering into formal supplier or other third-party relationships | A.15.1.3 | Direct | Supplier security assessment |
| GV.SC-07 | The risks posed by a supplier, their products, or their services are understood, recorded, prioritized, assessed, responded to, and monitored over the course of the relationship | A.15.1.3, A.15.2.1 | Direct | Ongoing supplier monitoring |
| GV.SC-08 | Relevant suppliers and other third parties are included in incident planning, response, and recovery activities | A.16.1.5, A.17.1.2 | Partial | Supplier incident coordination |
| GV.SC-09 | Supply chain security practices are integrated into cybersecurity and enterprise risk management programs, and their performance is monitored throughout the technology product and service lifecycle | A.15.1.1, 9.1 | Partial | Supplier performance monitoring |
| GV.SC-10 | Cybersecurity supply chain risk management plans include provisions for activities that occur during and after incidents | A.16.1.5, A.17.1.2 | Partial | Supplier incident response |

## IDENTIFY (ID) Function

### ID.AM: Asset Management
| NIST CSF 2.0 Subcategory | Description | ISO 27001 Mapping | Mapping Type | Notes |
|---------------------------|-------------|-------------------|--------------|-------|
| ID.AM-01 | Physical devices and systems within the organization are inventoried | A.8.1.1 | Direct | Asset inventory |
| ID.AM-02 | Software platforms and applications within the organization are inventoried | A.8.1.1 | Direct | Software asset inventory |
| ID.AM-03 | Organizational communication and data flows are mapped | A.13.2.1, A.8.2.1 | Partial | Network and data flow documentation |
| ID.AM-04 | External information systems are catalogued | A.8.1.1, A.15.1.1 | Partial | External system inventory |
| ID.AM-05 | Resources (e.g., hardware, devices, data, time, personnel, and software) are prioritized based on their classification, criticality, and business value | A.8.2.1, A.8.1.2 | Direct | Asset classification and ownership |
| ID.AM-06 | Cybersecurity roles and responsibilities for the entire workforce and third-party stakeholders are established | A.6.1.1, A.15.1.2 | Direct | Roles and responsibilities |
| ID.AM-07 | The organization's place in critical infrastructure and its industry sector is identified and communicated | 4.1, 4.2 | Partial | Organizational context |
| ID.AM-08 | Systems, hardware, software, services, and applications are managed throughout their lifecycles | A.8.1.1, A.14.2.1 | Partial | Asset lifecycle management |

### ID.RA: Risk Assessment
| NIST CSF 2.0 Subcategory | Description | ISO 27001 Mapping | Mapping Type | Notes |
|---------------------------|-------------|-------------------|--------------|-------|
| ID.RA-01 | Vulnerabilities in assets are identified, validated, and recorded | A.12.6.1, A.18.2.3 | Direct | Vulnerability management |
| ID.RA-02 | Cyber threat intelligence is received from information sharing forums and sources | A.6.1.4 | Partial | Threat intelligence gathering |
| ID.RA-03 | Threats, both internal and external, are identified and recorded | 6.1.2 | Direct | Threat identification in risk assessment |
| ID.RA-04 | Potential business impacts and likelihoods are identified | 6.1.2 | Direct | Risk analysis |
| ID.RA-05 | Threats, vulnerabilities, likelihoods, and impacts are used to understand inherent risk and inform risk response priorities | 6.1.2, 6.1.3 | Direct | Risk evaluation and treatment |
| ID.RA-06 | Risk responses are chosen, prioritized, planned, tracked, and communicated | 6.1.3 | Direct | Risk treatment |
| ID.RA-07 | Changes and exceptions are managed, assessed for risk impact, and communicated | 6.3, A.14.2.4 | Partial | Change management |
| ID.RA-08 | Processes for receiving, analyzing, and responding to vulnerability disclosures are established | A.16.1.3 | Partial | Vulnerability disclosure |
| ID.RA-09 | The authenticity and integrity of hardware and software are assessed prior to acquisition and use | A.14.1.3, A.15.1.3 | Partial | Secure acquisition |
| ID.RA-10 | Critical suppliers are assessed prior to acquisition and during use | A.15.1.3, A.15.2.1 | Direct | Supplier assessment |

## Implementation Recommendations

### Priority 1: Direct Mappings
Focus on implementing ISO 27001 controls that directly map to NIST CSF subcategories:
- Asset management controls (A.8.x.x)
- Risk management processes (6.1.x)
- Supplier security controls (A.15.x.x)

### Priority 2: Partial Mappings
Enhance existing ISO controls to better address NIST CSF requirements:
- Expand threat intelligence capabilities
- Improve supply chain risk management
- Enhance incident response coordination

### Priority 3: Gap Analysis
Identify and address gaps where NIST CSF subcategories are not fully covered:
- Formal threat intelligence program
- Supply chain incident response
- Critical infrastructure sector analysis

## Maintenance and Updates

This mapping shall be:
- Reviewed quarterly for accuracy and completeness
- Updated when either framework is revised
- Used to guide control implementation priorities
- Referenced during internal audits and assessments

---
*Document Classification: Internal Use Only*
*Next Review Date: [YYYY-MM-DD]*
