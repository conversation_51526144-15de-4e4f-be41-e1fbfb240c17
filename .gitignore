# ISO 27001 Compliance Repository .gitignore

# Sensitive Information - NEVER COMMIT
*.key
*.pem
*.p12
*.pfx
*password*
*secret*
*credential*
*token*
*.env
.env.*

# Personal Information
*personal*
*employee*
*customer*
*pii*
*gdpr*

# Evidence Files (link to external storage instead)
Evidence/actual_evidence/
Evidence/screenshots/
Evidence/documents/
Evidence/recordings/

# Temporary Files
*.tmp
*.temp
*~
.DS_Store
Thumbs.db

# Office Files
*.docx
*.xlsx
*.pptx
~$*

# Backup Files
*.bak
*.backup
*.old

# Log Files
*.log
logs/

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Compliance Tool Exports (if using external tools)
exports/
reports/
assessments/

# Draft Documents (use branches instead)
*draft*
*wip*
*work-in-progress*

# Vendor Specific
vendor/
node_modules/

# Compressed Files
*.zip
*.rar
*.7z
*.tar.gz

# Database Files
*.db
*.sqlite
*.sqlite3

# Configuration Files with Sensitive Data
config.json
settings.json
local.json
