# NIS2 Directive Specific Documentation

This directory contains documentation specific to the Network and Information Security Directive 2 (NIS2) compliance requirements that are not fully addressed by ISO 27001 controls or require additional sector-specific considerations.

## Purpose

The NIS2 Directive (EU 2022/2555) establishes cybersecurity requirements for essential and important entities across various sectors. While many NIS2 requirements align with ISO 27001 controls, certain obligations require specific documentation and procedures tailored to the directive's requirements.

## NIS2 Directive Overview

### Scope and Applicability
NIS2 applies to:
- **Essential Entities**: Critical infrastructure operators in specific sectors
- **Important Entities**: Entities in specific sectors meeting size criteria
- **Digital Service Providers**: Online marketplaces, search engines, cloud services

### Key Sectors Covered
- **Energy**: Electricity, oil, gas, hydrogen
- **Transport**: Air, rail, water, road transport
- **Banking and Financial Market Infrastructure**
- **Health**: Healthcare providers, pharmaceutical manufacturers
- **Drinking Water and Waste Water**
- **Digital Infrastructure**: Internet exchange points, DNS, TLD registries
- **ICT Service Management**: Managed service providers, managed security service providers
- **Public Administration**: Central and regional government bodies
- **Space**: Space-based services and ground-based infrastructure

## NIS2 Compliance Requirements

### Article 20: Governance
Management body responsibilities for cybersecurity risk management.

**Key Requirements**:
- Management body approval of cybersecurity risk management measures
- Oversight of cybersecurity risk management measures
- Participation in relevant cybersecurity training
- Understanding of cybersecurity risks and their impact

### Article 21: Cybersecurity Risk Management Measures
Comprehensive cybersecurity measures appropriate to entity size and risk.

**Required Measures**:
1. Policies on risk analysis and information system security
2. Incident handling procedures
3. Business continuity and crisis management
4. Supply chain security
5. Security in network and information systems acquisition, development, and maintenance
6. Policies and procedures for assessing effectiveness of measures
7. Basic cyber hygiene practices and cybersecurity training
8. Policies and procedures regarding cryptography and encryption
9. Human resources security, access control policies, and asset management
10. Use of multi-factor authentication or continuous authentication solutions
11. Secured voice, video, and text communications

### Article 23: Incident Reporting
Mandatory incident reporting to national authorities.

**Reporting Requirements**:
- **Initial Report**: Within 24 hours of becoming aware
- **Incident Notification**: Within 72 hours with detailed information
- **Final Report**: Within one month with comprehensive analysis
- **Significant Impact**: Immediate notification for incidents with significant impact

## Required Documents

### NIS2 Governance Documentation

#### Management Body Cybersecurity Responsibilities
**File**: `Management_Body_Cybersecurity_Responsibilities.md` (To be created)
**Status**: ❌ Required for applicable entities
**Purpose**: Document management body oversight and responsibilities for cybersecurity.

**Key Elements**:
- Management body composition and cybersecurity roles
- Cybersecurity risk management oversight procedures
- Management training and competency requirements
- Cybersecurity performance reporting to management
- Management decision-making authority for cybersecurity

#### Cybersecurity Risk Management Framework
**File**: `Cybersecurity_Risk_Management_Framework.md` (To be created)
**Status**: ❌ Required for applicable entities
**Purpose**: Comprehensive framework addressing all Article 21 requirements.

**Framework Components**:
- Risk analysis and information system security policies
- Incident handling procedures
- Business continuity and crisis management
- Supply chain security measures
- Security in acquisition, development, and maintenance
- Effectiveness assessment procedures

### NIS2 Incident Management

#### NIS2 Incident Reporting Procedures
**File**: `NIS2_Incident_Reporting_Procedures.md` (To be created)
**Status**: ❌ Required for applicable entities
**Purpose**: Specific procedures for NIS2 incident reporting requirements.

**Reporting Elements**:
- Incident classification and significance assessment
- 24-hour initial reporting procedures
- 72-hour detailed notification requirements
- Final report preparation and submission
- Coordination with national authorities

#### Incident Impact Assessment Framework
**File**: `Incident_Impact_Assessment_Framework.md` (To be created)
**Status**: ❌ Required for applicable entities
**Purpose**: Framework for assessing incident significance and impact.

**Assessment Criteria**:
- Service disruption thresholds
- User impact assessment
- Economic impact evaluation
- Reputational damage assessment
- Cross-border impact considerations

### Supply Chain Security

#### Supply Chain Cybersecurity Requirements
**File**: `Supply_Chain_Cybersecurity_Requirements.md` (To be created)
**Status**: ❌ Required for applicable entities
**Purpose**: Specific requirements for supply chain cybersecurity under NIS2.

**Requirements Include**:
- Supplier cybersecurity assessment procedures
- Supply chain risk management processes
- Contractual cybersecurity requirements
- Supplier monitoring and oversight
- Supply chain incident response coordination

### Sector-Specific Requirements

#### Sector-Specific Cybersecurity Measures
**File**: `Sector_Specific_Cybersecurity_Measures.md` (To be created)
**Status**: ❌ To be developed based on applicable sectors
**Purpose**: Additional cybersecurity measures specific to relevant sectors.

**Potential Sectors**:
- Energy sector specific requirements
- Healthcare sector specific requirements
- Financial sector specific requirements
- Transport sector specific requirements
- Digital infrastructure specific requirements

## NIS2 vs ISO 27001 Gap Analysis

### Areas of Strong Alignment
- Risk management processes
- Incident management procedures
- Asset management and protection
- Access control and authentication
- Business continuity planning
- Security awareness and training

### NIS2-Specific Requirements
- **Management Body Responsibilities**: Specific governance requirements
- **Incident Reporting Timelines**: Mandatory 24/72-hour reporting
- **Supply Chain Focus**: Enhanced supply chain security requirements
- **Sector-Specific Measures**: Industry-specific cybersecurity requirements
- **Continuous Authentication**: Specific authentication technology requirements
- **Secured Communications**: Requirements for communication security

### Additional Considerations
- **Proportionality**: Measures appropriate to entity size and risk
- **Cross-Border Coordination**: Coordination with other EU member states
- **National Authority Interaction**: Regular interaction with national cybersecurity authorities
- **Enforcement Mechanisms**: Administrative fines and penalties for non-compliance

## Implementation Approach

### Phase 1: Applicability Assessment (Weeks 1-2)
1. **Scope Determination**
   - Assess entity classification (essential vs. important)
   - Identify applicable sectors and activities
   - Determine size thresholds and criteria
   - Evaluate cross-border operations

2. **Gap Analysis**
   - Compare current ISO 27001 implementation with NIS2 requirements
   - Identify specific NIS2 requirements not covered by ISO 27001
   - Assess management body governance requirements
   - Evaluate incident reporting capabilities

### Phase 2: Governance Implementation (Weeks 3-6)
1. **Management Body Engagement**
   - Educate management on NIS2 responsibilities
   - Establish cybersecurity governance procedures
   - Implement management oversight mechanisms
   - Develop cybersecurity training for management

2. **Policy Enhancement**
   - Update existing policies to address NIS2 requirements
   - Develop NIS2-specific procedures and guidelines
   - Implement proportionate cybersecurity measures
   - Establish effectiveness assessment procedures

### Phase 3: Operational Implementation (Weeks 7-12)
1. **Incident Reporting Enhancement**
   - Implement NIS2 incident reporting procedures
   - Establish connections with national authorities
   - Develop incident impact assessment capabilities
   - Train incident response teams on NIS2 requirements

2. **Supply Chain Security**
   - Enhance supplier security assessment procedures
   - Update supplier contracts with cybersecurity requirements
   - Implement supply chain monitoring and oversight
   - Develop supply chain incident response coordination

### Phase 4: Monitoring and Compliance (Ongoing)
1. **Compliance Monitoring**
   - Regular assessment of NIS2 compliance
   - Monitoring of regulatory developments and guidance
   - Coordination with national cybersecurity authorities
   - Preparation for regulatory inspections and assessments

## Compliance Monitoring

### Regular Assessments
- **Monthly**: Incident reporting compliance review
- **Quarterly**: Cybersecurity measure effectiveness assessment
- **Semi-annually**: Supply chain security review
- **Annually**: Comprehensive NIS2 compliance assessment

### Key Performance Indicators
- Incident reporting timeline compliance (24/72-hour requirements)
- Management body cybersecurity training completion
- Supply chain security assessment completion rates
- Cybersecurity measure effectiveness ratings
- National authority interaction and coordination

### Regulatory Coordination
- Regular communication with national cybersecurity authorities
- Participation in sector-specific cybersecurity initiatives
- Coordination with other essential/important entities
- Monitoring of regulatory guidance and updates

## Integration with ISO 27001

### Shared Framework Benefits
- Unified risk management approach
- Integrated incident management procedures
- Common asset management and protection
- Shared governance and oversight mechanisms
- Coordinated training and awareness programs

### NIS2 Enhancements to ISO 27001
- Enhanced management governance requirements
- Specific incident reporting procedures
- Strengthened supply chain security measures
- Sector-specific cybersecurity considerations
- Regulatory coordination and reporting

## Templates and Tools

### Available Templates
- Management body responsibility matrix
- NIS2 incident reporting templates
- Supply chain security assessment templates
- Cybersecurity measure effectiveness assessment templates
- Regulatory coordination procedures

### Recommended Tools
- Incident reporting and management systems
- Supply chain risk management platforms
- Regulatory compliance monitoring tools
- Management dashboard and reporting systems
- Cybersecurity effectiveness measurement tools

## Review and Maintenance

### Review Schedule
- **Policies and Procedures**: Annual review and update
- **Incident Reporting Procedures**: Semi-annual review
- **Supply Chain Requirements**: Quarterly review
- **Management Governance**: Annual assessment

### Update Triggers
- Changes in NIS2 regulatory guidance
- New sector-specific requirements
- Organizational changes affecting scope
- Incident lessons learned
- National authority recommendations
- Cross-border coordination requirements

### Key Stakeholders
- **Primary Owner**: Chief Information Security Officer
- **Contributors**: Legal, Risk Management, Operations, IT
- **Reviewers**: Senior Management, Board of Directors
- **Approvers**: Management Body, Regulatory Affairs

---
*Last Updated: [Current Date]*
*Next Review: [Quarterly Review Date]*
*Note: This directory should only be populated if the organization is subject to NIS2 requirements*
